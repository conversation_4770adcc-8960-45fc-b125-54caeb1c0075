// ignore_for_file: prefer_function_declarations_over_variables

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:injectable/injectable.dart';

import '../data/getstore/get_store_helper.dart';
import '../di/components/service_locator.dart';
import '../features/home/<USER>';
import '../features/home/<USER>/category_items_page.dart';
import '../features/home/<USER>/media_list_view.dart';
import '../features/media_player/media_player_screen.dart';
import '../features/settings/screens/favorites_screen.dart';
import '../features/settings/screens/history_screen.dart';
import '../features/settings/screens/progress_screen.dart';
import '../features/settings/settings.dart';
import '../features/text_media_viewer/text_media_router_screen_riverpod.dart';
import '../features/tweets/tweets_list_screen.dart';
import 'fade_extension.dart';

GetStoreHelper getStoreHelper = getIt<GetStoreHelper>();

enum SGRoute {
  home,
  settings,
  login,
  register,
  forgotPassword,
  profile,
  editProfile,
  changePassword,
  mediaPlayer,
  textMediaViewer,
  tweets,
  favorites,
  history,
  progress;

  String get route {
    final String routeName = toString().replaceAll('SGRoute.', '');
    // debugPrint('Generated route for $this: /$routeName');
    return '/$routeName';
  }

  String get name => toString().replaceAll('SGRoute.', '');
}

@Singleton()
class SGGoRouter {
  final GoRouter goRoute = GoRouter(
    initialLocation: SGRoute.home.route,
    // debugLogDiagnostics: true, // Enable debug logging
    routes: <GoRoute>[
      // Define specific named routes first
      GoRoute(
        path: SGRoute.home.route,
        builder: (BuildContext context, GoRouterState state) =>
            const HomeScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.settings.route,
        builder: (BuildContext context, GoRouterState state) =>
            const SettingsScreen(),
      ).fade(),
      GoRoute(
        path: '${SGRoute.mediaPlayer.route}/:id',
        name: 'mediaPlayer', // Add a name for easier debugging
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to MediaPlayerScreen with id: $id');
          return MediaPlayerScreen(mediaId: id);
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.textMediaViewer.route}/:id',
        name: 'textMediaViewer', // Add a name for easier debugging
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          debugPrint('Navigating to TextMediaRouterScreen with id: $id');
          return TextMediaRouterScreen(mediaId: id);
        },
      ).fade(),
      GoRoute(
        path: SGRoute.tweets.route,
        name: 'tweets',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to TweetsListScreen');
          return const TweetsListScreen();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.favorites.route,
        name: 'favorites',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to FavoritesScreen');
          return const FavoritesScreen();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.history.route,
        name: 'history',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to HistoryScreen');
          return const HistoryScreen();
        },
      ).fade(),
      GoRoute(
        path: SGRoute.progress.route,
        name: 'progress',
        builder: (BuildContext context, GoRouterState state) {
          debugPrint('Navigating to ProgressScreen');
          return const ProgressScreen();
        },
      ).fade(),

      // Define generic pattern routes last
      GoRoute(
        path: '/:type/:category',
        name: 'categoryItems',
        builder: (BuildContext context, GoRouterState state) {
          final String type = state.pathParameters['type']!;
          final String category = state.pathParameters['category']!;
          debugPrint(
              'Navigating to CategoryItemsPage with type: $type, category: $category');
          return CategoryItemsPage(type: type, category: category);
        },
      ).fade(),
      GoRoute(
        path: '/:type',
        name: 'items',
        builder: (BuildContext context, GoRouterState state) {
          final String type = state.pathParameters['type']!;
          debugPrint('Navigating to ItemsPage with type: $type');
          return ItemsPage(type: type);
        },
      ).fade(),
    ],
  );
  GoRouter get getGoRouter => goRoute;
}

/// Example: Auth guard for Route Protection. GetStoreHelper is used to get token.
// ignore: unused_element
final String? Function(BuildContext context, GoRouterState state) _authGuard =
    (BuildContext context, GoRouterState state) {
  if (!(getStoreHelper.getToken() != null)) {
    return SGRoute.login.route;
  }
  return null;
};
