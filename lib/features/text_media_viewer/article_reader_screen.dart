import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../data/models/media_item.dart';
import '../../data/models/media_ui_model.dart';
import '../../utils/exit_confirmation.dart';
import '../navigation/navigation_provider.dart';
import 'providers/article_reader_provider.dart';
import 'widgets/document_download_dialog.dart';

class ArticleReaderScreen extends ConsumerStatefulWidget {
  const ArticleReaderScreen({
    required this.mediaItem,
    super.key,
  });

  final MediaUiModel mediaItem;

  @override
  ConsumerState<ArticleReaderScreen> createState() =>
      _ArticleReaderScreenState();
}

class _ArticleReaderScreenState extends ConsumerState<ArticleReaderScreen> {
  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    final String? htmlUrl = _getHtmlUrl();

    if (htmlUrl != null) {
      ref
          .read(articleReaderNotifierProvider.notifier)
          .initializeWithUrl(htmlUrl);
    } else {
      String? htmlContent;

      // First check metadata for HTML content
      if (widget.mediaItem.metadata?.html != null) {
        htmlContent = widget.mediaItem.metadata!.html;
        final int previewLength =
            htmlContent!.length >= 50 ? 50 : htmlContent.length;
        debugPrint(
            'Found HTML content in metadata: ${htmlContent.substring(0, previewLength)}...');
      }

      // Then check options for HTML content
      if (htmlContent == null && widget.mediaItem.options != null) {
        for (final MediaOption option in widget.mediaItem.options!) {
          if (option.format.toLowerCase() == 'html' && option.html != null) {
            htmlContent = option.html;
            debugPrint(
                'Found HTML content in option: ${htmlContent!.length} characters');
            break;
          }
        }
      }

      final String content = htmlContent ??
          widget.mediaItem.articleText ??
          widget.mediaItem.metadata?.description ??
          'No content available';

      final bool isHtml = content.trim().startsWith('<') &&
          (content.contains('</html>') ||
              content.contains('</body>') ||
              content.contains('</div>') ||
              content.contains('</p>'));

      debugPrint('Content is ${isHtml ? 'HTML' : 'plain text'}');

      ref
          .read(articleReaderNotifierProvider.notifier)
          .initializeWithHtml(content, isHtml: isHtml);
    }
  }

  String? _getHtmlUrl() {
    if (widget.mediaItem.metadata?.html != null) {
      return null;
    }

    // Check if we have an HTML option in the options array
    if (widget.mediaItem.options != null) {
      for (final MediaOption option in widget.mediaItem.options!) {
        final String format = option.format.toLowerCase();
        if (format.contains('html') &&
            option.url != null &&
            option.url!.isNotEmpty) {
          return option.url;
        }
      }
    }

    if (widget.mediaItem.metadata?.additionalInfo?.containsKey('options') ??
        false) {
      final List<dynamic>? options = widget
          .mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
      if (options != null) {
        for (final dynamic option in options) {
          if (option is Map<dynamic, dynamic> &&
              option['format'] == 'html' &&
              option['url'] != null) {
            return option['url'] as String;
          }
        }
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    final ArticleReaderState articleReaderState =
        ref.watch(articleReaderNotifierProvider);

    return ExitConfirmationWrapper(
      shouldConfirmExit: () => true,
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.mediaItem.title),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => ref.read(navigationProvider).safeGoBack(context),
            tooltip: 'رجوع',
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.text_decrease),
              onPressed: () => ref
                  .read(articleReaderNotifierProvider.notifier)
                  .decreaseFontSize(),
              tooltip: 'Decrease font size',
            ),
            IconButton(
              icon: const Icon(Icons.text_increase),
              onPressed: () => ref
                  .read(articleReaderNotifierProvider.notifier)
                  .increaseFontSize(),
              tooltip: 'Increase font size',
            ),
            IconButton(
              icon: Icon(
                articleReaderState.isDarkMode
                    ? Icons.light_mode
                    : Icons.dark_mode,
              ),
              onPressed: () => ref
                  .read(articleReaderNotifierProvider.notifier)
                  .toggleDarkMode(),
              tooltip: 'Toggle dark mode',
            ),
            // Download button
            Builder(
              builder: (BuildContext context) {
                // Only show download button if we have options
                final List<MediaOption> options =
                    widget.mediaItem.options ?? <MediaOption>[];

                // Only show the button if we have options with URLs
                final bool hasDownloadableOptions = options.any(
                    (MediaOption option) =>
                        option.url != null && option.url!.isNotEmpty);

                if (hasDownloadableOptions) {
                  return IconButton(
                    icon: const Icon(Icons.download_rounded),
                    onPressed: () {
                      showDocumentDownloadDialog(
                        context,
                        ref.read(articleReaderNotifierProvider.notifier),
                        mediaOptions: options,
                      );
                    },
                    tooltip: 'Download Document',
                  );
                } else {
                  return const SizedBox
                      .shrink(); // Hide if no downloadable options
                }
              },
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                final String shareText =
                    '${widget.mediaItem.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';
                SharePlus.instance.share(ShareParams(text: shareText));
              },
              tooltip: 'Share',
            ),
          ],
        ),
        body: Stack(
          children: <Widget>[
            if (articleReaderState.webViewController != null)
              WebViewWidget(controller: articleReaderState.webViewController!),
            if (articleReaderState.isLoading)
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }
}
