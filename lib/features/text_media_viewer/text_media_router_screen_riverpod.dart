import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/media_item.dart';
import '../../data/models/media_items/media_provider.dart';
import '../../data/models/media_items/media_type_enum.dart';
import '../../data/models/media_ui_model.dart';
import '../../utils/exit_confirmation.dart';
import '../navigation/navigation_provider.dart';
import 'article_reader_screen.dart';
import 'pdf_reader_screen.dart';
import 'text_media_viewer_screen.dart';

class TextMediaRouterScreen extends ConsumerWidget {
  const TextMediaRouterScreen({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(mediaUiListProvider).when(
          data: (List<MediaUiModel> items) {
            final MediaUiModel mediaItem = items.firstWhere(
              (MediaUiModel item) => item.id == mediaId,
              orElse: () => throw Exception('Media item not found'),
            );

            if (mediaItem == null) {
              return ExitConfirmationWrapper(
                shouldConfirmExit: () => true, // Always show confirmation
                child: Scaffold(
                  appBar: AppBar(
                    title: const Text('مستند'),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () {
                        // Use the navigation provider to handle back navigation
                        ref.read(navigationProvider).safeGoBack(context);
                      },
                      tooltip: 'رجوع',
                    ),
                  ),
                  body: const Center(child: Text('لم يتم العثور على العنصر')),
                ),
              );
            }

            // Determine which viewer to use based on media type and category
            final MediaType mediaType = MediaType.fromString(mediaItem.type);

            if (mediaType == MediaType.text) {
              // Check if it's a PDF document
              final bool isPdf = _isPdfDocument(mediaItem);

              if (isPdf) {
                return PdfReaderScreen(mediaItem: mediaItem);
              }

              // Check if it's an article or has HTML content
              if (mediaItem.category.toLowerCase() == 'articles' ||
                  mediaItem.category.toLowerCase() == 'html' ||
                  _hasHtmlContent(mediaItem)) {
                debugPrint(
                    'Routing to ArticleReaderScreen for item with HTML content: ${mediaItem.title}');
                return ArticleReaderScreen(mediaItem: mediaItem);
              }

              // Check if it's a book
              if (mediaItem.category.toLowerCase() == 'books') {
                // Check if it's a PDF document first
                if (_isPdfDocument(mediaItem)) {
                  return PdfReaderScreen(mediaItem: mediaItem);
                } else {
                  // For non-PDF books, use the regular text media viewer
                  return TextMediaViewerScreen(mediaId: mediaId);
                }
              }

              // Default to the original text media viewer for other cases
              return TextMediaViewerScreen(mediaId: mediaId);
            } else {
              // For non-text types, use the original viewer
              return TextMediaViewerScreen(mediaId: mediaId);
            }
          },
          loading: () => ExitConfirmationWrapper(
            shouldConfirmExit: () => true, // Always show confirmation
            child: Scaffold(
              appBar: AppBar(
                title: const Text('جاري التحميل...'),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Use the navigation provider to handle back navigation
                    ref.read(navigationProvider).safeGoBack(context);
                  },
                  tooltip: 'رجوع',
                ),
              ),
              body: const Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (Object error, StackTrace? stackTrace) =>
              ExitConfirmationWrapper(
            shouldConfirmExit: () => true, // Always show confirmation
            child: Scaffold(
              appBar: AppBar(
                title: const Text('خطأ'),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Use the navigation provider to handle back navigation
                    ref.read(navigationProvider).safeGoBack(context);
                  },
                  tooltip: 'رجوع',
                ),
              ),
              body: Center(child: Text('خطأ: $error')),
            ),
          ),
        );
  }

  bool _isPdfDocument(MediaUiModel mediaItem) {
    // Check if the document URL ends with .pdf
    if (mediaItem.documentUrl != null &&
        mediaItem.documentUrl!.toLowerCase().endsWith('.pdf')) {
      return true;
    }

    // Check if we have a PDF URL in options
    if (mediaItem.options != null && mediaItem.options!.isNotEmpty) {
      for (final MediaOption option in mediaItem.options!) {
        if (option.url != null && option.url!.isNotEmpty) {
          final String format = option.format.toLowerCase();
          if (format.contains('pdf')) {
            return true;
          }
        }
      }
    }

    // Check if we have options in additionalInfo
    if (mediaItem.metadata != null &&
        mediaItem.metadata!.additionalInfo != null &&
        mediaItem.metadata!.additionalInfo!.containsKey('options')) {
      final List<dynamic>? options =
          mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
      if (options != null) {
        for (final dynamic option in options) {
          if (option is Map<dynamic, dynamic> &&
              (option['format'] == 'application/pdf' ||
                  option['format'].toString().contains('pdf'))) {
            return true;
          }
        }
      }
    }

    return false;
  }

  bool _hasHtmlContent(MediaUiModel mediaItem) {
    // Check if the category is 'html'
    if (mediaItem.category.toLowerCase() == 'html') {
      return true;
    }

    // Check if we have HTML content in metadata
    if (mediaItem.metadata != null && mediaItem.metadata!.html != null) {
      return true;
    }

    // Check if we have HTML content in options
    if (mediaItem.options != null && mediaItem.options!.isNotEmpty) {
      for (final MediaOption option in mediaItem.options!) {
        final String format = option.format.toLowerCase();
        if (format == 'html' || format.contains('html')) {
          return true;
        }
      }
    }

    // Check if we have options in additionalInfo
    if (mediaItem.metadata != null &&
        mediaItem.metadata!.additionalInfo != null &&
        mediaItem.metadata!.additionalInfo!.containsKey('options')) {
      final List<dynamic>? options =
          mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
      if (options != null) {
        for (final dynamic option in options) {
          if (option is Map<dynamic, dynamic> && option['format'] == 'html') {
            return true;
          }
        }
      }
    }

    return false;
  }
}
