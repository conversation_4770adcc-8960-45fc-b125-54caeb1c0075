import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../data/models/media_item.dart';
import '../../data/models/media_ui_model.dart';
import '../../utils/exit_confirmation.dart';
import '../navigation/navigation_provider.dart';
import 'providers/pdf_reader_provider.dart';
import 'widgets/document_download_dialog.dart';

class PdfReaderScreen extends ConsumerStatefulWidget {
  const PdfReaderScreen({
    required this.mediaItem,
    super.key,
  });

  final MediaUiModel mediaItem;

  @override
  ConsumerState<PdfReaderScreen> createState() => _PdfReaderScreenState();
}

class _PdfReaderScreenState extends ConsumerState<PdfReaderScreen> {
  final PdfViewerController _pdfViewerController = PdfViewerController();

  @override
  void dispose() {
    _pdfViewerController.dispose();
    super.dispose();
  }

  String? _getPdfUrl() {
    // First check if we have a direct document URL
    if (widget.mediaItem.documentUrl != null &&
        widget.mediaItem.documentUrl!.isNotEmpty) {
      return widget.mediaItem.documentUrl;
    }

    // Then check if we have a PDF option in the options array
    if (widget.mediaItem.options != null) {
      for (final MediaOption option in widget.mediaItem.options!) {
        final String format = option.format.toLowerCase();
        if ((format.contains('pdf') || format.contains('application/pdf')) &&
            option.url != null &&
            option.url!.isNotEmpty) {
          return option.url;
        }
      }
    }

    if (widget.mediaItem.metadata != null &&
        widget.mediaItem.metadata!.additionalInfo != null &&
        widget.mediaItem.metadata!.additionalInfo!.containsKey('options')) {
      final List<dynamic>? options = widget
          .mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
      if (options != null) {
        for (final dynamic option in options) {
          if (option is Map<dynamic, dynamic> &&
              (option['format'] == 'application/pdf' ||
                  option['format'].toString().contains('pdf')) &&
              option['url'] != null) {
            return option['url'] as String;
          }
        }
      }
    }

    return null;
  }

  @override
  void initState() {
    super.initState();
    final String? pdfUrl = _getPdfUrl();
    if (pdfUrl == null) {
      ref
          .read(pdfReaderNotifierProvider.notifier)
          .setError('No PDF URL available for this document');
    }
  }

  @override
  Widget build(BuildContext context) {
    final PdfReaderState pdfReaderState = ref.watch(pdfReaderNotifierProvider);
    final String? pdfUrl = _getPdfUrl();

    return ExitConfirmationWrapper(
      shouldConfirmExit: () => true,
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.mediaItem.title),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(navigationProvider).safeGoBack(context);
            },
            tooltip: 'رجوع',
          ),
          actions: <Widget>[
            if (pdfUrl != null) ...<Widget>[
              IconButton(
                icon: const Icon(Icons.zoom_in),
                onPressed: () {
                  ref.read(pdfReaderNotifierProvider.notifier).increaseZoom();
                  _pdfViewerController.zoomLevel = pdfReaderState.zoomLevel;
                },
                tooltip: 'Zoom in',
              ),
              IconButton(
                icon: const Icon(Icons.zoom_out),
                onPressed: () {
                  ref.read(pdfReaderNotifierProvider.notifier).decreaseZoom();
                  _pdfViewerController.zoomLevel = pdfReaderState.zoomLevel;
                },
                tooltip: 'Zoom out',
              ),
              // Download button with beautiful dialog
              Builder(
                builder: (BuildContext context) {
                  // Get options for download
                  final List<MediaOption> options =
                      widget.mediaItem.options ?? <MediaOption>[];

                  return IconButton(
                    icon: pdfReaderState.isDownloading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.download_rounded),
                    onPressed: pdfReaderState.isDownloading
                        ? null
                        : () {
                            // Check if we have options with URLs
                            final bool hasDownloadableOptions = options.any(
                                (MediaOption option) =>
                                    option.url != null &&
                                    option.url!.isNotEmpty);

                            if (hasDownloadableOptions) {
                              // Show our beautiful download dialog
                              showDocumentDownloadDialog(
                                context,
                                ref.read(pdfReaderNotifierProvider.notifier),
                                mediaOptions: options,
                              );
                            } else if (pdfUrl != null) {
                              // Fallback to regular download
                              ref
                                  .read(pdfReaderNotifierProvider.notifier)
                                  .downloadFile(pdfUrl, widget.mediaItem.title);
                            }
                          },
                    tooltip: 'Download Document',
                  );
                },
              ),
            ],
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                final String shareText =
                    '${widget.mediaItem.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';
                SharePlus.instance.share(ShareParams(text: shareText));
              },
              tooltip: 'Share',
            ),
          ],
        ),
        body: Stack(
          children: <Widget>[
            if (pdfUrl != null)
              SfPdfViewer.network(
                pdfUrl,
                controller: _pdfViewerController,
                onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                  ref
                      .read(pdfReaderNotifierProvider.notifier)
                      .setLoading(false);
                },
                onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                  ref
                      .read(pdfReaderNotifierProvider.notifier)
                      .setError('Failed to load PDF: ${details.error}');
                },
              )
            else
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    const Icon(Icons.error_outline,
                        size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      pdfReaderState.errorMessage ?? 'No PDF URL available',
                      style: const TextStyle(fontSize: 18),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Go Back'),
                    ),
                  ],
                ),
              ),
            if (pdfReaderState.isLoading && pdfUrl != null)
              const Center(
                child: CircularProgressIndicator(),
              ),
            if (pdfReaderState.isDownloading)
              Container(
                color: Colors.black54,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      CircularProgressIndicator(
                        value: pdfReaderState.downloadProgress > 0
                            ? pdfReaderState.downloadProgress
                            : null,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'تحميل ملف: ${(pdfReaderState.downloadProgress * 100).toStringAsFixed(0)}%',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
