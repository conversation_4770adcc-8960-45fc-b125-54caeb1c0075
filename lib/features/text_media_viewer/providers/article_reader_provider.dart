import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:webview_flutter/webview_flutter.dart';

part 'article_reader_provider.g.dart';

/// State class for the article reader
class ArticleReaderState {
  const ArticleReaderState({
    this.isLoading = true,
    this.isDownloading = false,
    this.webViewController,
    this.fontSize = 16.0,
    this.isDarkMode = false,
  });
  final bool isLoading;
  final bool isDownloading;
  final WebViewController? webViewController;
  final double fontSize;
  final bool isDarkMode;

  /// Creates a copy of this state with the given fields replaced
  ArticleReaderState copyWith({
    bool? isLoading,
    bool? isDownloading,
    WebViewController? webViewController,
    double? fontSize,
    bool? isDarkMode,
  }) {
    return ArticleReaderState(
      isLoading: isLoading ?? this.isLoading,
      isDownloading: isDownloading ?? this.isDownloading,
      webViewController: webViewController ?? this.webViewController,
      fontSize: fontSize ?? this.fontSize,
      isDarkMode: isDarkMode ?? this.isDarkMode,
    );
  }
}

/// Provider for the article reader state
@riverpod
class ArticleReaderNotifier extends _$ArticleReaderNotifier {
  @override
  ArticleReaderState build() {
    return const ArticleReaderState();
  }

  /// Downloads a document with the specified URL and file type
  Future<void> downloadMedia({String? specificUrl, String? fileType}) async {
    if (specificUrl == null || specificUrl.isEmpty) {
      return;
    }

    // Set loading state
    state = state.copyWith(isDownloading: true);

    try {
      // Use the platform-specific download mechanism
      // This is typically handled by the OS's download manager
      if (state.webViewController != null) {
        await state.webViewController!.runJavaScript(
          '''
          var a = document.createElement('a');
          a.href = '$specificUrl';
          a.download = '${fileType ?? 'document'}';
          a.click();
          ''',
        );
      }
    } catch (e) {
      debugPrint('Error downloading document: $e');
    } finally {
      // Reset loading state
      state = state.copyWith(isDownloading: false);
    }
  }

  /// Initializes the WebView controller with a URL
  void initializeWithUrl(String url) {
    final WebViewController controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setLoading(true);
          },
          onPageFinished: (String url) {
            setLoading(false);
            applyStyles();
          },
        ),
      )
      ..loadRequest(Uri.parse(url));

    state = state.copyWith(webViewController: controller);
  }

  /// Initializes the WebView controller with HTML content
  void initializeWithHtml(String htmlContent, {bool isHtml = false}) {
    final String content = isHtml ? htmlContent : _wrapHtmlContent(htmlContent);

    final WebViewController controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            setLoading(false);
            applyStyles();
          },
        ),
      )
      ..loadHtmlString(content);

    state = state.copyWith(webViewController: controller);
  }

  /// Wraps plain text content in HTML
  String _wrapHtmlContent(String content) {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            direction: rtl;
            text-align: right;
            font-size: ${state.fontSize}px;
            color: ${state.isDarkMode ? '#ffffff' : '#333333'};
            background-color: ${state.isDarkMode ? '#121212' : '#ffffff'};
          }
          h1, h2, h3 {
            color: ${state.isDarkMode ? '#e1e1e1' : '#222222'};
          }
          a {
            color: ${state.isDarkMode ? '#90caf9' : '#1976d2'};
          }
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
          }
        </style>
      </head>
      <body>
        $content
      </body>
      </html>
    ''';
  }

  /// Applies styles to the WebView content
  void applyStyles() {
    if (state.webViewController == null) {
      return;
    }

    final String jsCode = '''
      document.body.style.fontSize = "${state.fontSize}px";
      document.body.style.color = "${state.isDarkMode ? '#ffffff' : '#333333'}";
      document.body.style.backgroundColor = "${state.isDarkMode ? '#121212' : '#ffffff'}";
      var headings = document.querySelectorAll('h1, h2, h3');
      for (var i = 0; i < headings.length; i++) {
        headings[i].style.color = "${state.isDarkMode ? '#e1e1e1' : '#222222'}";
      }
      var links = document.querySelectorAll('a');
      for (var i = 0; i < links.length; i++) {
        links[i].style.color = "${state.isDarkMode ? '#90caf9' : '#1976d2'}";
      }
    ''';

    state.webViewController!.runJavaScript(jsCode);
  }

  /// Increases the font size
  void increaseFontSize() {
    state = state.copyWith(fontSize: state.fontSize + 2);
    applyStyles();
  }

  /// Decreases the font size
  void decreaseFontSize() {
    final double newSize = (state.fontSize - 2).clamp(12.0, 32.0);
    state = state.copyWith(fontSize: newSize);
    applyStyles();
  }

  /// Toggles dark mode
  void toggleDarkMode() {
    state = state.copyWith(isDarkMode: !state.isDarkMode);
    applyStyles();
  }

  /// Sets the loading state
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }
}
