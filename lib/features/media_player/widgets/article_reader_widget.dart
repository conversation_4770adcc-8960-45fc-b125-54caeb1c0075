import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/media_items/media_player_controller.dart';
import '../../../data/models/media_player_state.dart';

class ArticleReaderWidget extends ConsumerWidget {
  const ArticleReaderWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final MediaPlayerController controller =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.mediaItem?.articleText == null) {
      return const Center(
        child: Text('No article content available'),
      );
    }

    final bool isDarkMode = mediaPlayerState.isDarkMode ?? false;
    final double fontSize = mediaPlayerState.articleFontSize ?? 16.0;

    // Create a scroll controller to track position
    final ScrollController scrollController = ScrollController(
      initialScrollOffset: mediaPlayerState.articleScrollPosition ?? 0.0,
    );

    // Listen to scroll changes
    scrollController.addListener(() {
      controller.setArticleScrollPosition(scrollController.offset);
    });

    return Column(
      children: <Widget>[
        // Article content
        Expanded(
          child: Container(
            color: isDarkMode ? Colors.black : Colors.white,
            child: SingleChildScrollView(
              controller: scrollController,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Title
                  Text(
                    mediaPlayerState.mediaItem!.title,
                    style: TextStyle(
                      fontSize: fontSize + 8,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Metadata if available
                  if (mediaPlayerState.mediaItem!.metadata != null &&
                      mediaPlayerState.mediaItem!.metadata!.description != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Text(
                        mediaPlayerState.mediaItem!.metadata!.description!,
                        style: TextStyle(
                          fontSize: fontSize - 2,
                          fontStyle: FontStyle.italic,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                      ),
                    ),

                  // Main content
                  Text(
                    mediaPlayerState.mediaItem!.articleText!,
                    style: TextStyle(
                      fontSize: fontSize,
                      height: 1.5,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Controls
        Container(
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.surface,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // Font size controls
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.text_decrease),
                    onPressed: () {
                      if (fontSize > 12.0) {
                        controller.setArticleFontSize(fontSize - 1.0);
                      }
                    },
                  ),
                  Text(
                    'Text Size: ${fontSize.toInt()}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  IconButton(
                    icon: const Icon(Icons.text_increase),
                    onPressed: () {
                      if (fontSize < 24.0) {
                        controller.setArticleFontSize(fontSize + 1.0);
                      }
                    },
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  ElevatedButton.icon(
                    icon: Icon(isDarkMode ? Icons.light_mode : Icons.dark_mode),
                    label: Text(isDarkMode ? 'Light Mode' : 'Dark Mode'),
                    onPressed: () => controller.toggleDarkMode(),
                  ),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.share),
                    label: const Text('Share'),
                    onPressed: () => controller.shareContent(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
