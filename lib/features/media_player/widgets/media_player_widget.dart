import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/media_items/media_player_controller.dart';
import '../../../data/models/media_player_state.dart';
import 'article_reader_widget.dart';
import 'audio_player_widget.dart';
import 'document_viewer_widget.dart';
import 'tweet_widget.dart';
import 'video_player_widget.dart';

class MediaPlayerWidget extends ConsumerWidget {
  const MediaPlayerWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.errorMessage != null) {
      return Center(
        child: Text(
          'Error: ${mediaPlayerState.errorMessage}',
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      );
    }

    if (mediaPlayerState.mediaItem == null) {
      return const Center(
        child: Text('No media item found'),
      );
    }

    // Determine which widget to show based on media type
    final String mediaType = mediaPlayerState.mediaItem!.type.toLowerCase();

    // Check for specific content types
    if (mediaType == 'audio' || mediaPlayerState.mediaItem!.audioUrl != null) {
      return AudioPlayerWidget(
        thumbnailUrl: mediaPlayerState.mediaItem!.thumbnailUrl,
        isPlaying: mediaPlayerState.isPlaying,
        audioPlayer: ref
            .read(mediaPlayerControllerProvider(mediaId).notifier)
            .audioPlayer,
      );
    } else if (mediaType == 'video' ||
        mediaPlayerState.mediaItem!.videoUrl != null) {
      return VideoPlayerWidget(
        chewieController: ref
            .read(mediaPlayerControllerProvider(mediaId).notifier)
            .chewieController,
      );
    } else if (mediaType == 'pdf' ||
        mediaType == 'document' ||
        mediaPlayerState.mediaItem!.documentUrl != null) {
      return DocumentViewerWidget(mediaId: mediaId);
    } else if (mediaType == 'article' ||
        mediaPlayerState.mediaItem!.articleText != null) {
      return ArticleReaderWidget(mediaId: mediaId);
    } else if (mediaType == 'tweet' ||
        mediaPlayerState.mediaItem!.tweetContent != null) {
      return TweetWidget(mediaId: mediaId);
    }

    // Fallback for unknown media types
    return Center(
      child: Text(
        'Unsupported media type: $mediaType',
        style: Theme.of(context).textTheme.titleLarge,
      ),
    );
  }
}
