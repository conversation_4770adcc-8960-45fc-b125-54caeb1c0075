import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/media_items/media_provider.dart';
import '../../../data/models/media_items/media_type_enum.dart';
import '../../../data/models/media_ui_model.dart';

class ItemsPage extends ConsumerWidget {
  const ItemsPage({required this.type, super.key});
  final String type;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<MediaUiModel> items = <MediaUiModel>[];

    // Handle special case for audio type
    if (type.toLowerCase() == 'audio') {
      items = ref.watch(filteredMediaProvider).where((MediaUiModel item) {
        final MediaType itemType = MediaType.fromString(item.type);
        return itemType == MediaType.audio ||
            (itemType == MediaType.tweet && item.audioUrl != null);
      }).toList();
    } else {
      items = ref.watch(filteredMediaProvider).where((MediaUiModel item) {
        final MediaType itemType = MediaType.fromString(item.type);
        final MediaType requestedType = MediaType.fromString(type);
        return itemType == requestedType;
      }).toList();
    }

    // Group items by category
    final Map<String, List<MediaUiModel>> categorizedItems =
        <String, List<MediaUiModel>>{};
    for (final MediaUiModel item in items) {
      if (!categorizedItems.containsKey(item.category)) {
        categorizedItems[item.category] = <MediaUiModel>[];
      }
      categorizedItems[item.category]!.add(item);
    }

    // Sort categories alphabetically
    final List<String> categories = categorizedItems.keys.toList()..sort();

    return Scaffold(
      appBar: AppBar(title: Text('فئات $type')),
      body: ListView.builder(
        itemCount: categories.length,
        itemBuilder: (BuildContext context, int index) {
          final String category = categories[index];
          final List<MediaUiModel> categoryItems = categorizedItems[category]!;

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: ListTile(
              title: Text(
                _formatCategoryName(category),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text('${categoryItems.length} items'),
              leading: Icon(
                _getIconForCategory(category),
                size: 40,
                color: Theme.of(context).primaryColor,
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Navigate to the category items page
                context.push('/$type/$category');
              },
            ),
          );
        },
      ),
    );
  }

  String _formatCategoryName(String category) {
    // Capitalize first letter of each word
    return category.split(' ').map((String word) {
      if (word.isEmpty) {
        return '';
      }
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  IconData _getIconForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'lessons':
        return Icons.school;
      case 'sermons':
        return Icons.record_voice_over;
      case 'lectures':
        return Icons.mic;
      case 'radio':
        return Icons.radio;
      case 'books':
        return Icons.book;
      case 'articles':
        return Icons.article;
      case 'photos':
        return Icons.photo_library;
      case 'videos':
        return Icons.video_library;
      default:
        return Icons.folder;
    }
  }
}
