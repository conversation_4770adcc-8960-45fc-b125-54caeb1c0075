import 'package:flutter/material.dart';

IconData getIconForType(String type) {
  switch (type.toLowerCase()) {
    case 'video':
      return Icons.video_library;
    case 'audio':
      return Icons.audiotrack;
    case 'pdf':
    case 'document':
      return Icons.insert_drive_file;
    case 'text':
      return Icons.article;
    case 'tweet':
      return Icons.chat;
    default:
      return Icons.insert_drive_file;
  }
}
