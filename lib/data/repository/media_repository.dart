import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/media_item.dart';
import '../models/media_items/media_type_enum.dart';
import '../models/media_ui_model.dart';
import '../services/media_loader_service.dart';

// Provider for MediaRepository
final Provider<MediaRepository> mediaRepositoryProvider =
    Provider<MediaRepository>((Ref ref) {
  return MediaRepository();
});

class MediaRepository {
  MediaRepository({MediaLoaderService? mediaLoaderService})
      : _mediaLoaderService = mediaLoaderService ?? MediaLoaderService();
  final MediaLoaderService _mediaLoaderService;

  /// Get all media items
  Future<List<MediaItem>> getAllMediaItems() async {
    return _mediaLoaderService.loadMediaItems();
  }

  /// Get all media items as UI models
  Future<List<MediaUiModel>> getAllMediaUiModels() async {
    return _mediaLoaderService.loadMediaUiModels();
  }

  /// Get media items by category
  Future<List<MediaUiModel>> getMediaByCategory(String category) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final List<MediaItem> filteredItems = allItems
        .where((MediaItem item) =>
            item.category.toLowerCase() == category.toLowerCase())
        .toList();
    return _mediaLoaderService.convertToUiModels(filteredItems);
  }

  /// Get media items by type
  Future<List<MediaUiModel>> getMediaByType(String type) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final MediaType requestedType = MediaType.fromString(type);
    final List<MediaItem> filteredItems = allItems
        .where((MediaItem item) =>
            MediaType.fromString(item.type) == requestedType)
        .toList();
    return _mediaLoaderService.convertToUiModels(filteredItems);
  }

  /// Get a media item by ID
  Future<MediaUiModel?> getMediaById(String id) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final MediaItem? item =
        allItems.where((MediaItem item) => item.id == id).firstOrNull;

    if (item == null) {
      return null;
    }

    return MediaUiModel.fromDomain(item);
  }

  /// Search media items
  Future<List<MediaUiModel>> searchMedia(String query) async {
    final List<MediaItem> allItems = await _mediaLoaderService.loadMediaItems();
    final String lowercaseQuery = query.toLowerCase();

    final List<MediaItem> filteredItems = allItems
        .where((MediaItem item) =>
            item.title.toLowerCase().contains(lowercaseQuery) ||
            (item.description?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            item.searchableText.toLowerCase().contains(lowercaseQuery))
        .toList();

    return _mediaLoaderService.convertToUiModels(filteredItems);
  }
}
