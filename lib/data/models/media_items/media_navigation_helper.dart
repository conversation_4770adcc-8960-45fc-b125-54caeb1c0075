import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../routing/app_router.dart';
import '../media_ui_model.dart';
import 'media_type_enum.dart';

/// Helper class for media navigation operations
class MediaNavigationHelper {
  // Constructor
  const MediaNavigationHelper();

  /// Navigates to the appropriate media player based on the media type
  void navigateToMediaPlayer(
    BuildContext context,
    MediaUiModel mediaItem,
  ) {
    final String mediaId = mediaItem.id;
    final MediaType mediaType = MediaType.fromString(mediaItem.type);
    final MediaCategory mediaCategory =
        MediaCategory.fromString(mediaItem.category);

    // Only handle audio and video types in the media player
    if (mediaType.isAudioOrVideo ||
        mediaItem.audioUrl != null ||
        mediaItem.videoUrl != null) {
      // Navigate to regular media player for audio/video
      debugPrint('Navigating to media player with ID: $mediaId');

      // Get the route explicitly to avoid any issues
      final String mediaPlayerRoute = SGRoute.mediaPlayer.route;
      debugPrint('mediaPlayer.route value: $mediaPlayerRoute');

      // Construct the full route
      final String fullRoute = '$mediaPlayerRoute/$mediaId';
      debugPrint('Full route: $fullRoute');

      // Use pushReplacement to avoid stacking navigation
      context.pushReplacement(fullRoute);
    } else if (mediaType == MediaType.tweet ||
        (mediaType == MediaType.text && mediaCategory == MediaCategory.tweet) ||
        mediaItem.tweetContent != null) {
      // Navigate to tweet details
      debugPrint('Navigating to tweet details with ID: $mediaId');

      final String tweetDetailsRoute = SGRoute.tweets.route;
      final String fullRoute = '$tweetDetailsRoute/$mediaId';
      // Use pushReplacement to avoid stacking navigation
      context.pushReplacement(fullRoute);
    } else if (mediaType.isDocument ||
        (mediaType == MediaType.text && mediaCategory != MediaCategory.html) ||
        (mediaItem.documentUrl != null && mediaItem.documentUrl!.isNotEmpty)) {
      // Navigate to text media viewer for PDF and document types
      debugPrint('Navigating to text media viewer with ID: $mediaId');

      final String textMediaViewerRoute = SGRoute.textMediaViewer.route;
      final String fullRoute = '$textMediaViewerRoute/$mediaId';
      context.pushReplacement(fullRoute);
    } else if (mediaType == MediaType.text &&
        (mediaCategory == MediaCategory.html ||
            (mediaItem.metadata != null && mediaItem.metadata!.html != null))) {
      // Navigate to article reader for HTML content
      debugPrint('Navigating to article reader with ID: $mediaId');

      final String articleReaderRoute = SGRoute.textMediaViewer.route;
      final String fullRoute = '$articleReaderRoute/$mediaId';
      context.pushReplacement(fullRoute);
    } else {
      // For any other type, show an error dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Unsupported Media Type'),
            content: Text(
                'The media type "${mediaItem.type}" is not supported for playback.'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }
}
