import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:just_audio/just_audio.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';

import '../media_item.dart';
import '../media_player_state.dart';
import '../media_ui_model.dart';
import 'media_provider.dart';
import 'media_type_enum.dart';

part 'media_player_controller.g.dart';

@riverpod
class MediaPlayerController extends _$MediaPlayerController {
  AudioPlayer? _audioPlayer;
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  bool _isDownloadedFile = false;
  String? _downloadedFilePath;

  @override
  MediaPlayerState build(String mediaId) {
    ref.onDispose(() {
      _disposeControllers();
    });

    // Initialize with a loading state
    final MediaPlayerState initialState = MediaPlayerState.initial();

    // Use Future.microtask to avoid calling state during build
    Future<void>.microtask(() => _loadMedia(mediaId));

    return initialState;
  }

  Future<void> _loadMedia(String mediaId) async {
    try {
      // First, set loading state
      state = state.copyWith(
        isLoading: true,
        isPlaying: false, // Ensure it's not playing by default
        errorMessage: null,
      );

      // Get the media items from the provider, but wait for data to be available
      List<MediaUiModel> mediaItems = <MediaUiModel>[];

      // Try to get the data from the provider
      await ref
          .watch(mediaUiListProvider.future)
          .then((List<MediaUiModel> data) {
        mediaItems = data;
      }).catchError((Object error) {
        debugPrint('MediaPlayerController: Error getting media items: $error');
        throw Exception('Failed to load media items: $error');
      });

      // Check if the list is empty
      if (mediaItems.isEmpty) {
        debugPrint('MediaPlayerController: No media items available');
        throw Exception('No media items available');
      }

      // Find the media item with the given ID

      final MediaUiModel mediaItem = mediaItems.firstWhere(
        (MediaUiModel item) => item.id == mediaId,
        orElse: () {
          debugPrint(
              'MediaPlayerController: Media item not found with ID: $mediaId');
          throw Exception('Media item not found with ID: $mediaId');
        },
      );

      // Update state with the found media item, but don't initialize players yet
      state = state.copyWith(
        mediaItem: mediaItem,
        isLoading:
            false, // Set loading to false since we're not loading media yet
        isPlaying: false, // Ensure it's not playing by default
      );

      debugPrint(
          'Media item loaded: ${mediaItem.title} (${mediaItem.type}) - Media will be prepared when play is pressed');

      // Set appropriate initial state based on media type
      final MediaType mediaType = MediaType.fromString(mediaItem.type);
      final MediaCategory mediaCategory =
          MediaCategory.fromString(mediaItem.category);

      if (mediaType.isDocument ||
          mediaType == MediaType.text ||
          (mediaItem.documentUrl != null &&
              mediaItem.documentUrl!.isNotEmpty)) {
        state = state.copyWith(
          isDocumentLoaded: false,
          currentPage: 1,
          totalPages: 0,
        );

        // Automatically load document for document types
        Future<void>.microtask(() => loadDocument());
      } else if (mediaType == MediaType.html || mediaItem.articleText != null) {
        state = state.copyWith(
          articleScrollPosition: 0.0,
          articleFontSize: 16.0,
        );
      } else if (mediaType == MediaType.tweet ||
          (mediaType == MediaType.text &&
              mediaCategory == MediaCategory.tweet) ||
          mediaItem.tweetContent != null) {
        state = state.copyWith(
          isLiked: false,
          likeCount: mediaItem.metadata?.likeCount ?? 0,
          retweetCount: mediaItem.metadata?.retweetCount ?? 0,
        );
      }

      // Don't initialize players here - we'll do it when the user presses play
      // This prevents any network requests until the user explicitly wants to play
    } catch (e) {
      // Update state with error message
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load media: $e',
      );
    }
  }

  Future<void> _initializeAudioPlayer(MediaUiModel mediaItem) async {
    // Dispose any existing controllers
    _disposeControllers();

    // Check if audio URL is available
    if (mediaItem.audioUrl == null || mediaItem.audioUrl!.isEmpty) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'لا يوجد رابط صوتي متاح لـ ${mediaItem.title}',
      );
      return;
    }

    try {
      // Create a new audio player
      _audioPlayer = AudioPlayer();

      // Set up listeners before setting the URL to catch any initialization errors

      // Listen to player state changes
      _audioPlayer!.playerStateStream.listen(
        (PlayerState playerState) {
          final bool isPlaying = playerState.playing;
          final ProcessingState processingState = playerState.processingState;

          state = state.copyWith(
            isPlaying: isPlaying,
            isBuffering: processingState == ProcessingState.buffering,
          );
        },
        onError: (Object e) {
          state = state.copyWith(
            isLoading: false,
            errorMessage: 'Audio player state error: $e',
          );
        },
      );

      // Listen to position changes
      _audioPlayer!.positionStream.listen(
        (Duration position) {
          state = state.copyWith(currentPosition: position);
        },
        onError: (Object e) {
          // Just log position errors, don't update state
          debugPrint('Position stream error: $e');
        },
      );

      // Listen to duration changes
      _audioPlayer!.durationStream.listen(
        (Duration? duration) {
          if (duration != null) {
            state = state.copyWith(totalDuration: duration);
          }
        },
        onError: (Object e) {
          // Just log duration errors, don't update state
          debugPrint('Duration stream error: $e');
        },
      );

      // Set the audio source with custom HTTP headers to bypass certificate validation
      final String audioUrl = mediaItem.audioUrl!;

      // Convert HTTP URLs to HTTPS if needed
      final String secureUrl = audioUrl.startsWith('http:')
          ? audioUrl.replaceFirst('http:', 'https:')
          : audioUrl;

      debugPrint('Using audio URL: $secureUrl');

      await _audioPlayer!.setUrl(
        secureUrl,
        // Add headers to help with potential SSL issues
        headers: <String, String>{
          'User-Agent': 'Mozilla/5.0',
          'Accept': '*/*',
        },
      );

      // Update state to indicate loading is complete, but don't auto-play
      state = state.copyWith(
        isLoading: false,
        isPlaying: false, // Ensure it's not playing by default
      );

      // Don't auto-play, wait for user to press play
      await _audioPlayer!.pause();
    } catch (e) {
      // Handle any exceptions during initialization
      state = state.copyWith(
        isLoading: false,
        isPlaying: false,
        errorMessage: 'فشل في تهيئة مشغل الصوت: $e',
      );
    }
  }

  Future<void> _initializeVideoPlayer(MediaUiModel mediaItem) async {
    // Dispose any existing controllers
    _disposeControllers();

    // Use the videoUrl field for video playback
    final String? videoUrl = mediaItem.videoUrl ??
        mediaItem.audioUrl; // Fallback to audioUrl if videoUrl is null

    // Check if video URL is available
    if (videoUrl == null || videoUrl.isEmpty) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'لا يوجد رابط فيديو متاح لـ ${mediaItem.title}',
      );
      return;
    }

    try {
      // Handle direct video links (including common video platforms)
      // If the URL is a direct video file (mp4, webm, etc.), use it directly
      final bool isDirectVideoFile = videoUrl.toLowerCase().endsWith('.mp4') ||
          videoUrl.toLowerCase().endsWith('.webm') ||
          videoUrl.toLowerCase().endsWith('.mov') ||
          videoUrl.toLowerCase().endsWith('.avi') ||
          videoUrl.contains('commondatastorage.googleapis.com') ||
          videoUrl.contains('storage.googleapis.com');

      // For YouTube, Facebook, TikTok, etc. we would need to extract the direct video URL
      // For now, we'll just use the direct URL if it's a direct video file

      // Convert HTTP URLs to HTTPS if needed
      final String secureUrl = videoUrl.startsWith('http:')
          ? videoUrl.replaceFirst('http:', 'https:')
          : videoUrl;

      debugPrint('Using video URL: $secureUrl');
      debugPrint('Is direct video file: $isDirectVideoFile');

      // Create a new video controller with HTTP headers to help with SSL issues
      _videoController = VideoPlayerController.network(
        secureUrl,
        httpHeaders: <String, String>{
          'User-Agent': 'Mozilla/5.0',
          'Accept': '*/*',
          'Origin': 'https://dralfarih.com',
          'Referer': 'https://dralfarih.com/',
        },
      );

      // Initialize the controller
      await _videoController!.initialize();

      // Initialize Chewie controller with enhanced features
      _chewieController = ChewieController(
        videoPlayerController: _videoController!,
        aspectRatio: _videoController!.value.aspectRatio,
        placeholder: const Center(child: CircularProgressIndicator()),
        errorBuilder: (BuildContext context, String errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  errorMessage,
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Retry loading the video
                    _disposeControllers();
                    Future<void>.microtask(
                        () => _initializeVideoPlayer(mediaItem));
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        },
      );

      // Set up a listener for video player changes
      _videoController!.addListener(() {
        if (_videoController != null && _videoController!.value.isInitialized) {
          state = state.copyWith(
            currentPosition: _videoController!.value.position,
            isPlaying: _videoController!.value.isPlaying,
            isBuffering: _videoController!.value.isBuffering,
          );
        }
      });

      // Make sure video is paused initially
      await _videoController!.pause();

      // Update state with video duration and loading complete
      state = state.copyWith(
        isLoading: false,
        isPlaying: false, // Ensure it's not playing by default
        totalDuration: _videoController!.value.duration,
      );
    } catch (e) {
      // Handle any exceptions during initialization
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'فشل في تهيئة مشغل الفيديو: $e',
      );
    }
  }

  void _disposeControllers() {
    _audioPlayer?.dispose();
    _audioPlayer = null;

    _chewieController?.dispose();
    _chewieController = null;

    _videoController?.dispose();
    _videoController = null;

    // Reset downloaded file state
    _isDownloadedFile = false;
    _downloadedFilePath = null;
  }

  // Document viewer methods
  Future<void> loadDocument() async {
    debugPrint('Loading document...');
    debugPrint('Media item: ${state.mediaItem}');
    debugPrint('Document URL: ${state.mediaItem?.documentUrl}');

    if (state.mediaItem == null || state.mediaItem!.documentUrl == null) {
      debugPrint('No document URL available');
      state = state.copyWith(
        documentLoadError: 'No document URL available',
        isDocumentLoaded: false,
      );
      return;
    }

    try {
      state = state.copyWith(
        isLoading: true,
        documentLoadError: null,
      );

      // Check if it's a PDF document
      final bool isPdfFile =
          state.mediaItem!.documentUrl!.toLowerCase().endsWith('.pdf');

      if (!isPdfFile) {
        // For non-PDF documents, we'll just mark it as loaded
        // The UI will handle displaying a link to open it externally
        debugPrint(
            'Non-PDF document detected: ${state.mediaItem!.documentUrl}');
        state = state.copyWith(
          isLoading: false,
          isDocumentLoaded: true,
          isPlaying: true,
          documentLoadError: null,
        );
        return;
      }

      // For PDF documents, download the file and set the path
      debugPrint('Is PDF file: $isPdfFile');

      // Download the PDF file
      final String documentUrl = state.mediaItem!.documentUrl!;
      final String fileName = documentUrl.split('/').last;

      // Get the app's temporary directory
      final Directory tempDir = await getTemporaryDirectory();
      final String filePath = '${tempDir.path}/$fileName';
      final File file = File(filePath);

      // Check if the file already exists
      if (await file.exists()) {
        debugPrint('PDF file already exists at: $filePath');
      } else {
        debugPrint('Downloading PDF file from: $documentUrl');

        // Create a Dio instance with retry capability
        final Dio dio = Dio();
        dio.interceptors.add(RetryInterceptor(
          dio: dio,
          logPrint: debugPrint,
          retryDelays: const <Duration>[
            Duration(seconds: 1),
            Duration(seconds: 2),
            Duration(seconds: 3),
          ],
        ));

        // Download the file
        await dio.download(
          documentUrl,
          filePath,
          onReceiveProgress: (int received, int total) {
            if (total != -1) {
              final double progress = received / total;
              debugPrint(
                  'Download progress: ${(progress * 100).toStringAsFixed(0)}%');
              state = state.copyWith(
                downloadProgress: progress,
                isDownloading: true,
              );
            }
          },
        );

        debugPrint('PDF file downloaded to: $filePath');
      }

      // Set the downloaded file path
      _downloadedFilePath = filePath;
      _isDownloadedFile = true;

      // Always set isPlaying to true for PDF files to ensure they auto-play
      state = state.copyWith(
        isLoading: false,
        isDocumentLoaded: true,
        isDownloading: false,
        // For PDF files, we'll set isPlaying to true to indicate they're automatically displayed
        isPlaying: isPdfFile, // This ensures PDF files are marked as playing
        currentPage: 1,
        totalPages: 0, // This will be updated by the PDF viewer widget
        pdfZoom: 1.0,
      );

      debugPrint('Document loaded. isPlaying set to: ${state.isPlaying}');
    } catch (e) {
      debugPrint('Error loading document: $e');
      state = state.copyWith(
        isLoading: false,
        isDocumentLoaded: false,
        isDownloading: false,
        documentLoadError: e.toString(),
      );
    }
  }

  Future<void> loadLocalDocument(File file) async {
    try {
      state = state.copyWith(
        isLoading: true,
        documentLoadError: null,
      );

      // Update the media item with the local file path
      final MediaUiModel updatedMediaItem = state.mediaItem!.copyWith(
        documentUrl: file.path,
      );

      // Set the state with the updated media item
      state = state.copyWith(
        mediaItem: updatedMediaItem,
        isLoading: false,
        isDocumentLoaded: true,
        currentPage: 1,
        totalPages: 0, // This will be updated by the PDF viewer widget
        pdfZoom: 1.0,
      );

      // Set downloaded file state
      _isDownloadedFile = true;
      _downloadedFilePath = file.path;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isDocumentLoaded: false,
        documentLoadError: 'Failed to load local document: $e',
      );
    }
  }

  void setCurrentPage(int page) {
    state = state.copyWith(currentPage: page);
  }

  void setPdfZoom(double zoom) {
    state = state.copyWith(pdfZoom: zoom);
  }

  void setPdfPageCount(int? pages) {
    if (pages != null) {
      state = state.copyWith(totalPages: pages);
    }
  }

  void setDocumentLoadError(String errorMessage) {
    state = state.copyWith(
      documentLoadError: errorMessage,
      isLoading: false,
    );
  }

  // Store the PDF view controller for later use
  PDFViewController? _pdfViewController;

  void setPdfViewController(PDFViewController controller) {
    _pdfViewController = controller;
    debugPrint('PDF view controller set');
  }

  // Method specifically for auto-playing PDF files
  Future<void> autoPlayPdf() async {
    if (state.mediaItem == null || state.mediaItem!.documentUrl == null) {
      debugPrint('Cannot auto-play PDF: No document URL available');
      return;
    }

    final bool isPdfFile =
        state.mediaItem!.documentUrl!.toLowerCase().endsWith('.pdf');
    if (!isPdfFile) {
      debugPrint('Cannot auto-play: Not a PDF file');
      return;
    }

    debugPrint('Auto-playing PDF file: ${state.mediaItem!.documentUrl}');

    // First set loading state
    state = state.copyWith(
      isLoading: true,
      documentLoadError: null,
    );

    try {
      // Force isPlaying to true for PDF files
      state = state.copyWith(
        isLoading: false,
        isDocumentLoaded: true,
        isPlaying: true,
        currentPage: 1,
        pdfZoom: 1.0,
      );

      debugPrint(
          'PDF auto-play state updated: isPlaying=${state.isPlaying}, isDocumentLoaded=${state.isDocumentLoaded}');

      // No need to call loadDocument again as it might cause a loop
      // The PDF viewer widget will handle the actual loading
    } catch (e) {
      debugPrint('Error in autoPlayPdf: $e');
      state = state.copyWith(
        isLoading: false,
        documentLoadError: e.toString(),
      );
    }
  }

  void updateMediaItem(MediaUiModel updatedItem) {
    state = state.copyWith(
      mediaItem: updatedItem,
      isDocumentLoaded: false, // Reset document loaded state
      documentLoadError: null, // Clear any previous errors
    );
  }

  Future<void> play() async {
    if (state.mediaItem == null) {
      return;
    }

    // Check if we should play a downloaded file
    if (_isDownloadedFile && _downloadedFilePath != null) {
      debugPrint('Playing downloaded file: $_downloadedFilePath');
      state = state.copyWith(isLoading: true);

      final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);

      if (mediaType == MediaType.audio) {
        // Initialize audio player with the downloaded file
        _disposeControllers();
        _audioPlayer = AudioPlayer();

        // Set up listeners
        _audioPlayer!.playerStateStream.listen(
          (PlayerState playerState) {
            final bool isPlaying = playerState.playing;
            final ProcessingState processingState = playerState.processingState;

            state = state.copyWith(
              isPlaying: isPlaying,
              isBuffering: processingState == ProcessingState.buffering,
            );
          },
        );

        // Set up position listener for audio player
        _audioPlayer!.positionStream.listen(
          (Duration position) {
            state = state.copyWith(currentPosition: position);
          },
        );

        _audioPlayer!.durationStream.listen(
          (Duration? duration) {
            if (duration != null) {
              state = state.copyWith(totalDuration: duration);
            }
          },
        );

        // Load and play the local file
        await _audioPlayer!.setFilePath(_downloadedFilePath!);
        await _audioPlayer!.play();
      } else if (MediaType.fromString(state.mediaItem!.type) ==
          MediaType.video) {
        // Initialize video player with the downloaded file
        _disposeControllers();
        _videoController =
            VideoPlayerController.file(File(_downloadedFilePath!));

        await _videoController!.initialize();

        _chewieController = ChewieController(
          videoPlayerController: _videoController!,
          aspectRatio: _videoController!.value.aspectRatio,
          showControls: false,
        );

        _videoController!.addListener(() {
          if (_videoController != null &&
              _videoController!.value.isInitialized) {
            state = state.copyWith(
              currentPosition: _videoController!.value.position,
              isPlaying: _videoController!.value.isPlaying,
              isBuffering: _videoController!.value.isBuffering,
            );
          }
        });

        state = state.copyWith(
          totalDuration: _videoController!.value.duration,
        );

        await _videoController!.play();
      }

      state = state.copyWith(
        isLoading: false,
        isPlaying: true,
      );

      return;
    }

    // If not playing a downloaded file, handle streaming media
    final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);

    // Handle different media types
    if (mediaType == MediaType.audio && _audioPlayer == null) {
      debugPrint('Initializing audio player on demand');
      state = state.copyWith(isLoading: true);
      await _initializeAudioPlayer(state.mediaItem!);
      // Don't auto-play, wait for user to press play
      state = state.copyWith(isLoading: false, isPlaying: false);
    } else if (mediaType == MediaType.video && _videoController == null) {
      debugPrint('Initializing video player on demand');
      state = state.copyWith(isLoading: true);
      await _initializeVideoPlayer(state.mediaItem!);
      // Don't auto-play, wait for user to press play
      state = state.copyWith(isLoading: false, isPlaying: false);
    } else if (mediaType == MediaType.pdf ||
        mediaType == MediaType.document ||
        (state.mediaItem!.documentUrl != null &&
            state.mediaItem!.documentUrl!.isNotEmpty)) {
      debugPrint('Loading document: ${state.mediaItem!.documentUrl}');
      state = state.copyWith(isLoading: true);
      await _initializeDocumentViewer(state.mediaItem!);
    } else if (mediaType == MediaType.html ||
        state.mediaItem!.articleText != null) {
      debugPrint('Loading article content');
      // No need to initialize anything for articles, just mark as loaded
      state = state.copyWith(
        isLoading: false,
        isPlaying: true, // In this context, "playing" means "viewing"
      );
    } else if (mediaType == MediaType.tweet ||
        state.mediaItem!.tweetContent != null) {
      debugPrint('Loading tweet content');
      // No need to initialize anything for tweets, just mark as loaded
      state = state.copyWith(
        isLoading: false,
        isPlaying: true, // In this context, "playing" means "viewing"
      );
    } else {
      // Players are already initialized, but don't auto-play
      // We'll let the user explicitly press the play button
      debugPrint(
          'Media player already initialized, ready for user to press play');
    }

    // Don't set isPlaying to true automatically
    // The actual play action will happen when the user presses the play button
    state = state.copyWith(isLoading: false);
  }

  // Explicit play method to be called when user presses play button
  Future<void> playMedia() async {
    if (state.mediaItem == null) {
      return;
    }

    final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);

    // Make sure the player is initialized
    if (mediaType == MediaType.audio && _audioPlayer == null) {
      await play(); // This will initialize but not auto-play
    } else if (mediaType == MediaType.video && _videoController == null) {
      await play(); // This will initialize but not auto-play
    }

    // Now explicitly play the media
    if (mediaType == MediaType.audio && _audioPlayer != null) {
      await _audioPlayer!.play();
      state = state.copyWith(isPlaying: true);
    } else if (mediaType == MediaType.video && _videoController != null) {
      await _videoController!.play();
      state = state.copyWith(isPlaying: true);
    }
  }

  void pause() {
    if (state.mediaItem?.type.toLowerCase() == 'audio') {
      _audioPlayer?.pause();
    } else if (state.mediaItem?.type.toLowerCase() == 'video') {
      _videoController?.pause();
    }

    state = state.copyWith(isPlaying: false);
  }

  // Reset the downloaded file flag
  void resetDownloadedFile() {
    _isDownloadedFile = false;
    _downloadedFilePath = null;
  }

  // Initialize document viewer (PDF, DOC, etc.)
  Future<void> _initializeDocumentViewer(MediaUiModel mediaItem) async {
    try {
      final String? documentUrl = mediaItem.documentUrl;

      if (documentUrl == null || documentUrl.isEmpty) {
        state = state.copyWith(
          isLoading: false,
          documentLoadError: 'No document URL available',
        );
        return;
      }

      // For now, we'll just mark the document as ready to be loaded
      // The actual loading will happen in the UI when the document widget is created
      state = state.copyWith(
        isLoading: false,
        isDocumentLoaded: true,
        isPlaying: true, // In this context, "playing" means "viewing"
      );

      debugPrint('Document ready to be loaded: $documentUrl');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        documentLoadError: 'فشل في تهيئة عارض المستندات: $e',
      );
    }
  }

  void seekTo(Duration position) {
    if (state.mediaItem?.type.toLowerCase() == 'audio') {
      _audioPlayer?.seek(position);
    } else if (state.mediaItem?.type.toLowerCase() == 'video') {
      _videoController?.seekTo(position);
    }

    state = state.copyWith(currentPosition: position);
  }

  void setVolume(double volume) {
    if (state.mediaItem?.type.toLowerCase() == 'audio') {
      _audioPlayer?.setVolume(volume);
    } else if (state.mediaItem?.type.toLowerCase() == 'video') {
      _videoController?.setVolume(volume);
    }

    state = state.copyWith(volume: volume);
  }

  void setPlaybackSpeed(double speed) {
    if (state.mediaItem?.type.toLowerCase() == 'audio') {
      _audioPlayer?.setSpeed(speed);
    } else if (state.mediaItem?.type.toLowerCase() == 'video') {
      _videoController?.setPlaybackSpeed(speed);
    }

    state = state.copyWith(playbackSpeed: speed);
  }

  // Get the downloads directory path
  Future<String> getDownloadsPath() async {
    try {
      // Try to get the downloads directory
      if (Platform.isAndroid) {
        // For Android, use the Download directory
        final Directory? externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final String downloadPath = externalDir.path
              .replaceAll('/Android/data/com.example.app/files', '/Download');
          final Directory downloadDir = Directory(downloadPath);
          if (!await downloadDir.exists()) {
            await downloadDir.create(recursive: true);
          }
          return downloadPath;
        }
      } else if (Platform.isIOS) {
        // For iOS, use the Documents directory
        final Directory docDir = await getApplicationDocumentsDirectory();
        final String downloadPath = '${docDir.path}/Downloads';
        final Directory downloadDir = Directory(downloadPath);
        if (!await downloadDir.exists()) {
          await downloadDir.create(recursive: true);
        }
        return downloadPath;
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        // For desktop platforms, use the Downloads directory
        final Directory homeDir = Directory(Platform.environment['HOME'] ?? '');
        final String downloadPath = '${homeDir.path}/Downloads';
        return downloadPath;
      }

      // Fallback to app documents directory
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      return appDocDir.path;
    } catch (e) {
      debugPrint('Error getting downloads path: $e');
      // Fallback to app documents directory
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      return appDocDir.path;
    }
  }

  // Check if a file is already downloaded
  Future<bool> isMediaDownloaded(String url) async {
    try {
      final String downloadPath = await getDownloadsPath();
      final String fileName = url.split('/').last;
      final String filePath = '$downloadPath/$fileName';
      final File file = File(filePath);
      return await file.exists();
    } catch (e) {
      debugPrint('Error checking if file exists: $e');
      return false;
    }
  }

  // Get the local file path for a downloaded file
  Future<String?> getDownloadedFilePath(String url) async {
    try {
      final String downloadPath = await getDownloadsPath();
      final String fileName = url.split('/').last;
      final String filePath = '$downloadPath/$fileName';
      final File file = File(filePath);
      if (await file.exists()) {
        return filePath;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting downloaded file path: $e');
      return null;
    }
  }

  // Check if we have a downloaded file and mark it for playback
  Future<void> checkDownloadedFile() async {
    if (state.mediaItem == null) {
      return;
    }

    try {
      String? url;
      final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);
      if (mediaType == MediaType.audio) {
        url = state.mediaItem!.audioUrl;
      } else if (mediaType == MediaType.video) {
        url = state.mediaItem!.videoUrl ?? state.mediaItem!.audioUrl;
      }

      if (url == null || url.isEmpty) {
        state = state.copyWith(
          errorMessage: 'No media URL available',
        );
        return;
      }

      // Check if the file is downloaded
      final String? localPath = await getDownloadedFilePath(url);
      if (localPath == null) {
        state = state.copyWith(
          errorMessage: 'File not downloaded yet. Please download first.',
        );
        return;
      }

      // Store the path for later use when play is pressed
      _downloadedFilePath = localPath;
      _isDownloadedFile = true;

      // Just notify that the file is ready, don't initialize players yet
      state = state.copyWith(
        errorMessage: 'Downloaded file ready. Press play to start playback.',
      );
      // Clear the message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        if (state.errorMessage ==
            'Downloaded file ready. Press play to start playback.') {
          state = state.copyWith(errorMessage: null);
        }
      });
    } catch (e) {
      state = state.copyWith(
        errorMessage: 'Failed to play downloaded file: $e',
      );
    }
  }

  // PDF navigation methods
  void goToPage(int pageNumber) {
    if (state.mediaItem == null ||
        state.totalPages == 0 ||
        pageNumber < 1 ||
        pageNumber > state.totalPages!) {
      return;
    }

    state = state.copyWith(currentPage: pageNumber);
  }

  void nextPage() {
    if (state.mediaItem == null ||
        state.currentPage == null ||
        state.totalPages == null ||
        state.currentPage! >= state.totalPages!) {
      return;
    }

    state = state.copyWith(currentPage: state.currentPage! + 1);
  }

  void previousPage() {
    if (state.mediaItem == null ||
        state.currentPage == null ||
        state.currentPage! <= 1) {
      return;
    }

    state = state.copyWith(currentPage: state.currentPage! - 1);
  }

  void setTotalPages(int totalPages) {
    state = state.copyWith(totalPages: totalPages);
  }

  // Article reading methods
  void setArticleScrollPosition(double position) {
    state = state.copyWith(articleScrollPosition: position);
  }

  void setArticleFontSize(double fontSize) {
    state = state.copyWith(articleFontSize: fontSize);
  }

  void toggleDarkMode() {
    state = state.copyWith(isDarkMode: !(state.isDarkMode ?? false));
  }

  // Tweet interaction methods
  void toggleLike() {
    final bool currentLiked = state.isLiked ?? false;
    final int currentLikeCount = state.likeCount ?? 0;

    state = state.copyWith(
      isLiked: !currentLiked,
      likeCount: currentLiked ? currentLikeCount - 1 : currentLikeCount + 1,
    );
  }

  void retweet() {
    final int currentRetweetCount = state.retweetCount ?? 0;

    state = state.copyWith(
      retweetCount: currentRetweetCount + 1,
    );
  }

  // Share content based on media type
  Future<void> shareContent() async {
    if (state.mediaItem == null) {
      return;
    }

    try {
      final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);
      String? shareText;
      String? shareUrl;

      if (mediaType == MediaType.audio || mediaType == MediaType.video) {
        // Share media URL
        shareText =
            'Check out this ${mediaType.toString().split('.').last}: ${state.mediaItem!.title}';
        shareUrl = mediaType == MediaType.audio
            ? state.mediaItem!.audioUrl
            : state.mediaItem!.videoUrl;
      } else if (mediaType == MediaType.pdf ||
          mediaType == MediaType.document ||
          mediaType == MediaType.text) {
        // Share document URL
        shareText = 'Check out this document: ${state.mediaItem!.title}';

        // Check if we have options with document URLs
        if (state.mediaItem!.options != null &&
            state.mediaItem!.options!.isNotEmpty) {
          // Create a map of document types to URLs
          final Map<String, String> documentUrls = <String, String>{};

          // Extract document URLs from options
          for (final MediaOption option in state.mediaItem!.options!) {
            if (option.url != null && option.url!.isNotEmpty) {
              final String format = option.format.toLowerCase();

              if (format.contains('pdf')) {
                documentUrls['pdf'] = option.url!;
              } else if (format.contains('docx') ||
                  format.contains('msword') ||
                  format.contains(
                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
                documentUrls['docx'] = option.url!;
              } else if (format.contains('text/plain')) {
                documentUrls['txt'] = option.url!;
              } else if (format.contains('html')) {
                documentUrls['html'] = option.url!;
              }
            }
          }

          // Prefer PDF for sharing
          if (documentUrls.containsKey('pdf')) {
            shareUrl = documentUrls['pdf'];
          } else if (documentUrls.containsKey('docx')) {
            shareUrl = documentUrls['docx'];
          } else if (documentUrls.isNotEmpty) {
            // Use the first available URL
            shareUrl = documentUrls.values.first;
          }
        }

        // If no URL found in metadata, use the document URL
        if (shareUrl == null || shareUrl.isEmpty) {
          shareUrl = state.mediaItem!.documentUrl;
        }
      } else if (mediaType == MediaType.html) {
        // Share article text
        shareText = 'Article: ${state.mediaItem!.title}\n\n';
        if (state.mediaItem!.articleText != null) {
          // Limit the text to a reasonable length for sharing
          final String articlePreview =
              state.mediaItem!.articleText!.length > 300
                  ? '${state.mediaItem!.articleText!.substring(0, 300)}...'
                  : state.mediaItem!.articleText!;
          shareText += articlePreview;
        }
      } else if (mediaType == MediaType.tweet) {
        // Share tweet
        shareText =
            'Tweet by ${state.mediaItem!.tweetAuthor ?? 'Unknown'}:\n\n${state.mediaItem!.tweetContent ?? ''}';
      }

      if (shareText != null) {
        if (shareUrl != null && shareUrl.isNotEmpty) {
          shareText += '\n\n$shareUrl';
        }

        await SharePlus.instance.share(ShareParams(text: shareText));
      }
    } catch (e) {
      debugPrint('Error sharing content: $e');
      state = state.copyWith(
        errorMessage: 'Failed to share content: $e',
      );
    }
  }

  Future<void> downloadMedia({String? specificUrl, String? fileType}) async {
    if (state.mediaItem == null || (state.isDownloading ?? true)) {
      return;
    }

    // Get the appropriate URL based on media type
    String? url;
    final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);

    // If a specific URL is provided, use it
    if (specificUrl != null && specificUrl.isNotEmpty) {
      url = specificUrl;
      debugPrint('Using specific URL for download: $url');
    } else {
      // Otherwise, get URL based on media type
      if (mediaType == MediaType.audio) {
        url = state.mediaItem!.audioUrl;
      } else if (mediaType == MediaType.video) {
        url = state.mediaItem!.videoUrl ?? state.mediaItem!.audioUrl;
      } else if (mediaType == MediaType.pdf ||
          mediaType == MediaType.document ||
          mediaType == MediaType.text) {
        // For document types, check if we have options with document URLs
        if (state.mediaItem!.options != null &&
            state.mediaItem!.options!.isNotEmpty) {
          // Create a map of document types to URLs
          final Map<String, String> documentUrls = <String, String>{};

          // Extract document URLs from options
          for (final MediaOption option in state.mediaItem!.options!) {
            if (option.url != null && option.url!.isNotEmpty) {
              final String format = option.format.toLowerCase();

              if (format.contains('pdf')) {
                documentUrls['pdf'] = option.url!;
              } else if (format.contains('docx') ||
                  format.contains('msword') ||
                  format.contains(
                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
                documentUrls['docx'] = option.url!;
              } else if (format.contains('text/plain')) {
                documentUrls['txt'] = option.url!;
              } else if (format.contains('html')) {
                documentUrls['html'] = option.url!;
              } else if (format.startsWith('audio')) {
                documentUrls['audio'] = option.url!;
              } else if (format.startsWith('video')) {
                documentUrls['video'] = option.url!;
              }
            }
          }

          // If a specific file type is requested, try to get that URL
          if (fileType != null && documentUrls.containsKey(fileType)) {
            url = documentUrls[fileType];
            debugPrint('Using $fileType URL from options: $url');
          }
          // Otherwise use PDF by default, or fall back to any available document URL
          else if (documentUrls.containsKey('pdf')) {
            url = documentUrls['pdf'];
            debugPrint('Using PDF URL from options: $url');
          } else if (documentUrls.containsKey('docx')) {
            url = documentUrls['docx'];
            debugPrint('Using DOCX URL from options: $url');
          } else if (documentUrls.containsKey('txt')) {
            url = documentUrls['txt'];
            debugPrint('Using TXT URL from options: $url');
          } else if (documentUrls.isNotEmpty) {
            // Use the first available URL
            url = documentUrls.values.first;
            debugPrint('Using first available document URL from options: $url');
          }
        }

        // If still no URL, try the document URL
        if (url == null || url.isEmpty) {
          url = state.mediaItem!.documentUrl;
        }
      }
    }

    if (url == null || url.isEmpty) {
      state = state.copyWith(
        errorMessage: 'No media URL available for download',
      );
      return;
    }

    // Check if the file is already downloaded
    final bool isDownloaded = await isMediaDownloaded(url);
    if (isDownloaded) {
      final String downloadPath = await getDownloadsPath();
      final String fileName = url.split('/').last;
      final String filePath = '$downloadPath/$fileName';

      state = state.copyWith(
        errorMessage: 'File already downloaded: $filePath',
      );

      // Clear the message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        if (state.errorMessage?.contains('File already downloaded') ?? true) {
          state = state.copyWith(errorMessage: null);
        }
      });

      return;
    }

    state = state.copyWith(
      isDownloading: true,
      downloadProgress: 0.0,
      errorMessage: null,
    );

    try {
      final String downloadPath = await getDownloadsPath();
      final String fileName = url.split('/').last;
      final String savePath = '$downloadPath/$fileName';

      // Convert HTTP URLs to HTTPS if needed
      final String secureUrl =
          url.startsWith('http:') ? url.replaceFirst('http:', 'https:') : url;

      // debugPrint('Downloading from URL: $secureUrl');
      // debugPrint('Saving to: $savePath');

      // Create a more optimized Dio instance for faster downloads
      final Dio dio = Dio(
        BaseOptions(
          connectTimeout: const Duration(
              seconds: 15), // Reduced timeout for faster connection
          receiveTimeout:
              const Duration(seconds: 120), // Increased for larger files
          sendTimeout: const Duration(seconds: 15), // Reduced timeout
          headers: <String, String>{
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache', // Avoid caching issues
            'Pragma': 'no-cache', // Avoid caching issues
          },
          responseType: ResponseType.stream,
          // Increase buffer size for faster downloads
          contentType: 'application/octet-stream',
          listFormat: ListFormat.multiCompatible,
        ),
      );

      // Add interceptors for better performance
      dio.interceptors.add(
        InterceptorsWrapper(
          onRequest:
              (RequestOptions options, RequestInterceptorHandler handler) {
            options.followRedirects = true;
            options.validateStatus =
                (int? status) => status != null && status < 500;

            // Add additional headers for specific file types
            final String fileExt = path.extension(savePath).toLowerCase();
            if (fileExt == '.docx') {
              options.headers['Accept'] =
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            } else if (fileExt == '.pdf') {
              options.headers['Accept'] = 'application/pdf';
            }

            return handler.next(options);
          },
          onError: (DioException error, ErrorInterceptorHandler handler) {
            // debugPrint('Download error: ${error.message}');
            // Try to recover from some errors
            if (error.type == DioExceptionType.connectionTimeout) {
              // Retry with increased timeout
              final RequestOptions options = error.requestOptions;
              options.connectTimeout = const Duration(seconds: 30);
              final RequestOptions newOptions = options;
              return handler.resolve(
                Response<dynamic>(
                  requestOptions: newOptions,
                  statusCode: 499, // Custom status code for retry
                ),
              );
            }
            return handler.next(error);
          },
        ),
      );

      // Add a retry interceptor
      dio.interceptors.add(
        RetryInterceptor(
          dio: dio,
          logPrint: debugPrint,
          retryDelays: const <Duration>[
            Duration(seconds: 1), // Wait 1 second before first retry
            Duration(seconds: 2), // Wait 2 seconds before second retry
            Duration(seconds: 3), // Wait 3 seconds before third retry
          ],
        ),
      );

      // Use a more efficient download method
      final Response<ResponseBody> response = await dio.get<ResponseBody>(
        secureUrl,
        options: Options(responseType: ResponseType.stream),
      );

      final File file = File(savePath);
      final IOSink sink = file.openWrite();
      final int? contentLength =
          int.tryParse(response.headers.value('content-length') ?? '0');
      int received = 0;

      await response.data!.stream.listen(
        (List<int> chunk) {
          sink.add(chunk);
          received += chunk.length;
          if (contentLength != null && contentLength > 0) {
            final double progress = received / contentLength;
            state = state.copyWith(downloadProgress: progress);
          }
        },
        onDone: () async {
          await sink.flush();
          await sink.close();
        },
        onError: (dynamic error) {
          sink.close();
          throw error as Object;
        },
      ).asFuture<void>();

      // Show success message
      state = state.copyWith(
        isDownloading: false,
        downloadProgress: 1.0,
      );

      debugPrint('Download completed successfully. File saved to: $savePath');

      // Try to open the file after download
      try {
        // Get file extension to determine appropriate action
        final String fileExt = path.extension(savePath).toLowerCase();
        debugPrint('File extension: $fileExt');

        if (Platform.isAndroid || Platform.isIOS) {
          // On mobile, try to open the file with the default app
          // For DOCX files, we might need special handling
          if (fileExt == '.docx') {
            // Show a notification that the file is downloaded but may need a specific app
            state = state.copyWith(
              errorMessage:
                  'DOCX file downloaded to Downloads folder. You may need Microsoft Word or another compatible app to open it.',
            );

            // Try to open it anyway
            final OpenResult result = await OpenFile.open(savePath);
            if (result.type != ResultType.done) {
              debugPrint('Could not open DOCX file: ${result.message}');
            }
          } else {
            // For other file types, just try to open
            final OpenResult result = await OpenFile.open(savePath);
            if (result.type != ResultType.done) {
              debugPrint('Could not open file: ${result.message}');
            }
          }
        } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
          // On desktop, try to reveal the file in finder/explorer
          if (Platform.isMacOS) {
            // On macOS, reveal the file in Finder
            final ProcessResult result =
                await Process.run('open', <String>['-R', savePath]);
            if (result.exitCode != 0) {
              debugPrint('Failed to reveal file in Finder: ${result.stderr}');
              // Try to open the Downloads folder instead
              await Process.run('open', <String>[path.dirname(savePath)]);
            }
          } else if (Platform.isWindows) {
            // On Windows, select the file in Explorer
            final ProcessResult result =
                await Process.run('explorer', <String>['/select,', savePath]);
            if (result.exitCode != 0) {
              debugPrint('Failed to reveal file in Explorer: ${result.stderr}');
              // Try to open the Downloads folder instead
              await Process.run('explorer', <String>[path.dirname(savePath)]);
            }
          } else if (Platform.isLinux) {
            // On Linux, open the containing folder
            final ProcessResult result =
                await Process.run('xdg-open', <String>[path.dirname(savePath)]);
            if (result.exitCode != 0) {
              debugPrint('Failed to open folder: ${result.stderr}');
            }
          }
        }
      } catch (e) {
        debugPrint('Error opening file: $e');
      }

      // Create a more specific success message based on file type
      final String fileExt = path.extension(savePath).toLowerCase();
      String successMessage;

      if (fileExt == '.pdf') {
        successMessage = 'PDF downloaded successfully to Downloads folder.';
      } else if (fileExt == '.docx') {
        successMessage =
            'Word document (DOCX) downloaded successfully to Downloads folder.';
      } else if (fileExt == '.txt') {
        successMessage =
            'Text file downloaded successfully to Downloads folder.';
      } else {
        successMessage = 'File downloaded successfully to: $savePath';
      }

      // Update state with success message
      state = state.copyWith(
        errorMessage: successMessage,
      );

      // Clear the success message after 5 seconds
      Future.delayed(const Duration(seconds: 5), () {
        if ((state.errorMessage?.contains('downloaded successfully') ?? true) ||
            (state.errorMessage?.contains('Download completed successfully') ??
                true)) {
          state = state.copyWith(errorMessage: null);
        }
      });
    } catch (e) {
      state = state.copyWith(
        isDownloading: false,
        errorMessage: 'Failed to download media: $e',
      );
    }
  }

  Future<void> shareMedia() async {
    if (state.mediaItem == null) {
      return;
    }

    final String? url = state.mediaItem!.audioUrl ??
        state.mediaItem!.videoUrl ??
        state.mediaItem!.documentUrl;
    if (url == null) {
      state = state.copyWith(
        errorMessage: 'No media URL available for sharing',
      );
      return;
    }

    try {
      // Use the custom share template
      final String shareText =
          '${state.mediaItem!.title} $url --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';

      await SharePlus.instance.share(
        ShareParams(
            text: shareText, subject: 'Sharing ${state.mediaItem!.title}'),
      );
    } catch (e) {
      state = state.copyWith(
        errorMessage: 'Failed to share media: $e',
      );
    }
  }

  // Method to show captions (description)
  void showCaptions(BuildContext context) {
    if (state.mediaItem == null) {
      return;
    }

    final String? description = state.mediaItem!.metadata?.description;

    if (description == null || description.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No description available')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Description'),
          content: SingleChildScrollView(
            child: Text(description),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Method to play the next audio in the list
  Future<void> playNextAudio() async {
    if (state.mediaItem == null) {
      return;
    }

    // Get all audio items
    final List<MediaUiModel> allItems =
        await ref.read(mediaUiListProvider.future);
    final List<MediaUiModel> audioItems = allItems
        .where((MediaUiModel item) =>
            MediaType.fromString(item.type) == MediaType.audio)
        .toList();

    if (audioItems.isEmpty) {
      return;
    }

    // Find the current item index
    final int currentIndex = audioItems
        .indexWhere((MediaUiModel item) => item.id == state.mediaItem!.id);

    if (currentIndex == -1 || currentIndex >= audioItems.length - 1) {
      // If current item not found or it's the last item, play the first item
      final String nextId = audioItems[0].id;
      // Reset the current controller
      _disposeControllers();
      // Build a new controller for the next item
      ref.read(mediaPlayerControllerProvider(nextId).notifier);
    } else {
      // Play the next item
      final String nextId = audioItems[currentIndex + 1].id;
      // Reset the current controller
      _disposeControllers();
      // Build a new controller for the next item
      ref.read(mediaPlayerControllerProvider(nextId).notifier);
    }
  }

  VideoPlayerController? get videoController => _videoController;
  ChewieController? get chewieController => _chewieController;
  AudioPlayer? get audioPlayer => _audioPlayer;

  // Get the current downloaded file path (for PDF viewer)
  String? getCurrentFilePath() => _downloadedFilePath;

  // Play downloaded media file if available
  Future<void> playDownloadedMedia() async {
    if (state.mediaItem == null) {
      return;
    }

    // If we already have a downloaded file path, initialize it but don't auto-play
    if (_isDownloadedFile && _downloadedFilePath != null) {
      // Initialize but don't auto-play
      final MediaType mediaType = MediaType.fromString(state.mediaItem!.type);
      final String? documentUrl = state.mediaItem!.documentUrl;
      final bool isPdfFile =
          documentUrl != null && documentUrl.toLowerCase().endsWith('.pdf');

      // Handle PDF and document files
      if (isPdfFile ||
          mediaType == MediaType.text ||
          mediaType == MediaType.document) {
        // For PDF files, we'll open them directly
        try {
          final OpenResult result = await OpenFile.open(_downloadedFilePath);
          if (result.type != ResultType.done) {
            state = state.copyWith(
              isLoading: false,
              errorMessage: 'Could not open file: ${result.message}',
            );
          } else {
            state = state.copyWith(
              isLoading: false,
              errorMessage: null,
            );
          }
        } catch (e) {
          state = state.copyWith(
            isLoading: false,
            errorMessage: 'Error opening file: $e',
          );
        }
        return;
      } else if (mediaType == MediaType.audio) {
        // Initialize audio player with the downloaded file
        _disposeControllers();
        _audioPlayer = AudioPlayer();

        // Set up listeners (similar to what's in the play method)
        _audioPlayer!.playerStateStream.listen(
          (PlayerState playerState) {
            final bool isPlaying = playerState.playing;
            final ProcessingState processingState = playerState.processingState;

            state = state.copyWith(
              isPlaying: isPlaying,
              isBuffering: processingState == ProcessingState.buffering,
            );
          },
        );

        _audioPlayer!.positionStream.listen(
          (Duration position) {
            state = state.copyWith(currentPosition: position);
          },
        );

        _audioPlayer!.durationStream.listen(
          (Duration? duration) {
            if (duration != null) {
              state = state.copyWith(totalDuration: duration);
            }
          },
        );

        // Load the file but don't play it
        await _audioPlayer!.setFilePath(_downloadedFilePath!);
        state = state.copyWith(isLoading: false);
      } else if (mediaType == 'video') {
        // Initialize video player with the downloaded file
        _disposeControllers();
        _videoController =
            VideoPlayerController.file(File(_downloadedFilePath!));

        await _videoController!.initialize();

        _chewieController = ChewieController(
          videoPlayerController: _videoController!,
          aspectRatio: _videoController!.value.aspectRatio,
          showControls: false,
        );

        _videoController!.addListener(() {
          if (_videoController != null &&
              _videoController!.value.isInitialized) {
            state = state.copyWith(
              currentPosition: _videoController!.value.position,
              isPlaying: _videoController!.value.isPlaying,
              isBuffering: _videoController!.value.isBuffering,
            );
          }
        });

        state = state.copyWith(
          totalDuration: _videoController!.value.duration,
          isLoading: false,
        );
      }

      return;
    }

    // Otherwise, check if the file is downloaded first
    state = state.copyWith(isLoading: true);
    await checkDownloadedFile();

    // If the file was found and marked for playback, initialize it but don't auto-play
    if (_isDownloadedFile && _downloadedFilePath != null) {
      // Call the method again to initialize the player
      await playDownloadedMedia();
    } else {
      // If no downloaded file was found, show a message
      state = state.copyWith(
        isLoading: false,
        errorMessage:
            'No downloaded file found. Please download the media first.',
      );

      // Clear the message after 3 seconds
      Future<void>.delayed(const Duration(seconds: 3), () {
        if (state.errorMessage ==
            'No downloaded file found. Please download the media first.') {
          state = state.copyWith(errorMessage: null);
        }
      });
    }
  }

  /// Opens a URL in the browser
  Future<void> openInBrowser(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Could not launch $url');
        state = state.copyWith(
          errorMessage: 'Could not open URL: $url',
        );
      }
    } catch (e) {
      debugPrint('Error opening URL: $e');
      state = state.copyWith(
        errorMessage: 'Error opening URL: $e',
      );
    }
  }
}

@riverpod
MediaUiModel? mediaItemDetails(Ref ref, String mediaId) {
  final List<MediaUiModel> mediaItems = ref.watch(filteredMediaProvider);
  return mediaItems.firstWhere(
    (MediaUiModel item) => item.id == mediaId,
    orElse: () => throw Exception('Media item not found'),
  );
}
