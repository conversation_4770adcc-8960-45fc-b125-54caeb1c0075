import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../gen/assets.gen.dart';
import '../media_items/media_provider.dart';
import '../media_ui_model.dart';
import 'tab_category_model.dart';

part 'tab_category_provider.g.dart';

/// Provider for main tab categories
@riverpod
Future<List<MainTabCategory>> mainTabCategories(Ref ref) async {
  // Load the JSON data
  final String jsonString = await rootBundle.loadString(Assets.jsons.samples);
  final Map<String, dynamic> jsonData =
      json.decode(jsonString) as Map<String, dynamic>;

  // Extract media items
  final List<dynamic> mediaItems = jsonData['mediaItems'] as List<dynamic>;

  // Get unique categories from media items
  final Set<String> audioCategories = <String>{};
  final Set<String> videoCategories = <String>{};
  final Set<String> textCategories = <String>{};

  for (final dynamic item in mediaItems) {
    final Map<String, dynamic> mediaItem = item as Map<String, dynamic>;
    final String type = mediaItem['type'] as String;
    final String category = mediaItem['category'] as String;

    if (type == 'audio') {
      audioCategories.add(category);
    } else if (type == 'video') {
      videoCategories.add(category);
    } else if (type == 'text') {
      textCategories.add(category);
    }
  }

  // Create main tab categories
  final List<MainTabCategory> mainCategories = <MainTabCategory>[
    // Audio tab
    MainTabCategory(
      id: 'audio',
      title: 'Audio',
      titleAr: 'الصوتيات',
      icon: Icons.audiotrack,
      secondaryTabs: audioCategories.map((String category) {
        String titleAr;
        switch (category) {
          case 'lessons':
            titleAr = 'الدروس العلمية';
            break;
          case 'sermons':
            titleAr = 'الخطب';
            break;
          case 'lectures':
            titleAr = 'المحاضرات';
            break;
          case 'radio':
            titleAr = 'الإذاعة';
            break;
          default:
            titleAr = 'نصائح';
        }

        return SecondaryTabCategory(
          id: category,
          title: _formatCategoryName(category),
          titleAr: titleAr,
          mediaType: 'audio',
        );
      }).toList(),
    ),

    // Video tab
    MainTabCategory(
      id: 'video',
      title: 'Video',
      titleAr: 'المرئيات',
      icon: Icons.videocam,
      secondaryTabs: videoCategories.map((String category) {
        String titleAr;
        switch (category) {
          case 'lectures':
            titleAr = 'الدروس المرئية';
            break;
          case 'tv_shows':
            titleAr = 'اللقاءات التلفزيونية';
            break;
          default:
            titleAr = 'المناسبات';
        }

        return SecondaryTabCategory(
          id: category,
          title: _formatCategoryName(category),
          titleAr: titleAr,
          mediaType: 'video',
        );
      }).toList(),
    ),

    // References tab
    MainTabCategory(
      id: 'references',
      title: 'References',
      titleAr: 'المرجعيات',
      icon: Icons.menu_book,
      secondaryTabs: <SecondaryTabCategory>[
        ...textCategories.map((String category) {
          String titleAr;
          switch (category) {
            case 'books':
              titleAr = 'الكتب';
              break;
            case 'articles':
              titleAr = 'المقالات';
              break;
            default:
              titleAr = 'البحوث';
          }

          return SecondaryTabCategory(
            id: category,
            title: _formatCategoryName(category),
            titleAr: titleAr,
            mediaType: 'text',
          );
        }).toList(),
      ],
    ),

    // Tweets tab (dedicated tab for tweets)
    MainTabCategory(
      id: 'tweets',
      title: 'Tweets',
      titleAr: 'التغريدات',
      icon: Icons.format_quote,
      secondaryTabs: <SecondaryTabCategory>[
        SecondaryTabCategory(
          id: 'recent_tweets',
          title: 'Recent Tweets',
          titleAr: 'التغريدات الحديثة',
          mediaType: 'tweet',
        ),
        // SecondaryTabCategory(
        //   id: 'popular_tweets',
        //   title: 'Popular Tweets',
        //   titleAr: 'التغريدات الشائعة',
        //   mediaType: 'tweet',
        // ),
        // SecondaryTabCategory(
        //   id: 'audio_tweets',
        //   title: 'Audio Tweets',
        //   titleAr: 'التغريدات الصوتية',
        //   mediaType: 'tweet',
        // ),
      ],
    ),
  ];

  return mainCategories;
}

/// Provider for filtered media items based on selected category
@riverpod
List<MediaUiModel> categoryFilteredMedia(
  Ref ref, {
  required String mediaType,
  required String category,
}) {
  final List<MediaUiModel> allMedia = ref.watch(filteredMediaProvider);

  if (mediaType.toLowerCase() == 'audio') {
    return allMedia
        .where((MediaUiModel item) =>
            (item.type.toLowerCase() == 'audio' ||
                (item.type.toLowerCase() == 'tweet' &&
                    item.audioUrl != null)) &&
            item.category.toLowerCase() == category.toLowerCase())
        .toList();
  } else {
    return allMedia
        .where((MediaUiModel item) =>
            item.type.toLowerCase() == mediaType.toLowerCase() &&
            item.category.toLowerCase() == category.toLowerCase())
        .toList();
  }
}

/// Helper function to format category names
String _formatCategoryName(String category) {
  // Capitalize first letter of each word
  return category.split('_').map((String word) {
    if (word.isEmpty) {
      return '';
    }
    return word[0].toUpperCase() + word.substring(1);
  }).join(' ');
}

/// Provider for the selected main tab index
@Riverpod(keepAlive: true)
class SelectedMainTab extends _$SelectedMainTab {
  @override
  int build() {
    return 0;
  }

  void setTab(int index) {
    state = index;
  }
}

/// Provider for the selected secondary tab index
@Riverpod(keepAlive: true)
class SelectedSecondaryTab extends _$SelectedSecondaryTab {
  @override
  int build() {
    return 0;
  }

  void setTab(int index) {
    state = index;
  }
}

/// Provider for the selected media ID in the mini player
@Riverpod(keepAlive: true)
class MiniPlayerMediaId extends _$MiniPlayerMediaId {
  @override
  String? build() {
    return null;
  }

  void setMediaId(String? id) {
    state = id;
  }

  void clearMediaId() {
    state = null;
  }
}
